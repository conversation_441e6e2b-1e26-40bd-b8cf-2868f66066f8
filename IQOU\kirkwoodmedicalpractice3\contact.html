<!DOCTYPE html>
<html lang="en-US">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="https://gmpg.org/xfn/11">
	<title>Contact - Kirkwood Medical Practice</title>
	<meta name="description" content="Let's Stay Connected, Neighbor to Neighbor. Contact Kirkwood Medical Practice for appointments and healthcare needs.">
	<link rel="icon" href="./wp-content/uploads/2023/06/cropped-Kirwood-Medical-Practice-Logo-2-32x32.png" sizes="32x32">
	<link rel="icon" href="./wp-content/uploads/2023/06/cropped-Kirwood-Medical-Practice-Logo-2-192x192.png" sizes="192x192">
	<link rel="apple-touch-icon" href="./wp-content/uploads/2023/06/cropped-Kirwood-Medical-Practice-Logo-2-180x180.png">
	<meta name="msapplication-TileImage" content="./wp-content/uploads/2023/06/cropped-Kirwood-Medical-Practice-Logo-2-270x270.png">
	<link rel="stylesheet" id="kadence-blocks-rowlayout-css" href="./wp-content/plugins/kadence-blocks/dist/assets/css/kb-layout.min.css?ver=3.2.40" media="all">
	<link rel="stylesheet" id="kadence-global-css" href="./wp-content/themes/kadence/assets/css/global.min.css?ver=1.2.7" media="all">
	<link rel="stylesheet" id="kadence-content-css" href="./wp-content/themes/kadence/assets/css/content.min.css?ver=1.2.7" media="all">
	<style>
/* ===== CSS VARIABLES ===== */
:root {
    --primary-blue: #2c5aa0;
    --secondary-blue: #4a7bc8;
    --accent-blue: #6bb6ff;
    --light-blue: #e8f2ff;
    --accent-green: #28a745;
    --light-gray: #f8f9fa;
    --medium-gray: #e9ecef;
    --dark-gray: #343a40;
    --text-dark: #2c3e50;
    --text-light: #6c757d;
    --white: #ffffff;
    --shadow-light: 0 2px 10px rgba(44, 90, 160, 0.1);
    --shadow-medium: 0 4px 20px rgba(44, 90, 160, 0.15);
    --shadow-heavy: 0 8px 30px rgba(44, 90, 160, 0.2);
    --border-radius: 8px;
    --border-radius-large: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --font-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-secondary: Georgia, 'Times New Roman', serif;
}

/* ===== GLOBAL TYPOGRAPHY ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-dark);
    font-size: 16px;
    margin: 0;
    padding: 0;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--primary-blue);
    font-weight: 700;
}

p {
    margin-bottom: 1.2rem;
    color: var(--text-light);
    line-height: 1.7;
}

/* ===== NAVIGATION STYLING ===== */
.site-header {
    background: var(--white);
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.main-navigation {
    margin: 0 20px;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 8px;
    align-items: center;
}

.nav-menu li {
    margin: 0;
}

.nav-link {
    display: block;
    padding: 12px 18px;
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: 2px solid transparent;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover, .nav-link.active {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    border-color: var(--primary-blue);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* ===== BUTTON STYLING ===== */
.button, .kb-button, .header-button {
    display: inline-block;
    padding: 12px 24px;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white) !important;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.95rem;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.button::before, .kb-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.button:hover::before, .kb-button:hover::before {
    left: 100%;
}

.button:hover, .kb-button:hover, .header-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
}

.button-outline {
    background: transparent;
    color: var(--primary-blue) !important;
    border: 2px solid var(--primary-blue);
}

.button-outline:hover {
    background: var(--primary-blue);
    color: var(--white) !important;
}

/* ===== PAGE HERO ===== */
.page-hero {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--white);
    padding: 100px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.page-hero h1 {
    position: relative;
    z-index: 2;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-hero p {
    position: relative;
    z-index: 2;
    font-size: 1.1rem;
    opacity: 0.95;
    max-width: 700px;
    margin: 0 auto;
}

/* ===== PAGE CONTENT ===== */
.page-content {
    padding: 80px 0;
}

/* ===== CONTACT INFO GRID ===== */
.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin: 50px 0;
}

.contact-info-item {
    background: var(--white);
    padding: 35px;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    text-align: center;
    border: 1px solid var(--medium-gray);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.contact-info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
    transform: scaleX(0);
    transition: var(--transition);
}

.contact-info-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.contact-info-item:hover::before {
    transform: scaleX(1);
}

.contact-info-item h3 {
    color: var(--primary-blue);
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.contact-info-item p {
    color: var(--text-light);
    margin: 12px 0;
    line-height: 1.6;
}

.contact-info-item a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.contact-info-item a:hover {
    color: var(--secondary-blue);
    text-decoration: underline;
}

/* ===== CTA SECTION ===== */
.cta-section {
    background: linear-gradient(135deg, var(--light-blue), var(--white));
    padding: 60px 40px;
    border-radius: var(--border-radius-large);
    text-align: center;
    margin: 60px 0;
    border: 2px solid var(--medium-gray);
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
}

.cta-section h2 {
    color: var(--primary-blue);
    margin-bottom: 15px;
}

.cta-section p {
    margin-bottom: 30px;
    font-size: 1.1rem;
}

/* ===== CONTACT FORM ===== */
.contact-form {
    background: var(--white);
    padding: 50px;
    border-radius: var(--border-radius-large);
    margin-top: 50px;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--medium-gray);
    position: relative;
    overflow: hidden;
}

.contact-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
}

.contact-form h3 {
    color: var(--primary-blue);
    margin-bottom: 30px;
    text-align: center;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-dark);
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius);
    font-size: 16px;
    font-family: var(--font-primary);
    transition: var(--transition);
    background: var(--white);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-submit {
    text-align: center;
    margin-top: 30px;
}

.form-submit button {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    padding: 16px 32px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.form-submit button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.form-submit button:hover::before {
    left: 100%;
}

.form-submit button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .nav-menu {
        gap: 5px;
    }

    .nav-link {
        padding: 10px 14px;
        font-size: 0.9rem;
    }

    .contact-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }

    .main-navigation {
        margin: 10px 0;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.25rem; }

    .page-hero {
        padding: 80px 0;
    }

    .page-content {
        padding: 60px 0;
    }

    .contact-info-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .contact-info-item {
        padding: 25px;
    }

    .contact-form {
        padding: 30px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cta-section {
        padding: 40px 20px;
        margin: 40px 0;
    }
}

@media (max-width: 480px) {
    .nav-link {
        padding: 12px;
        text-align: center;
    }

    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }

    .page-hero {
        padding: 60px 0;
    }

    .page-content {
        padding: 40px 0;
    }

    .button, .kb-button {
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
    }

    .contact-form {
        padding: 20px;
    }

    .contact-info-item {
        padding: 20px;
    }

    .form-submit button {
        width: 100%;
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== EXISTING THEME OVERRIDES ===== */
.has-theme-palette-3-color {
    color: var(--primary-blue) !important;
}

.has-theme-palette7-background-color {
    background-color: var(--light-gray) !important;
}

.button-style-filled {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue)) !important;
    border-color: var(--primary-blue) !important;
}

.button-style-filled:hover {
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue)) !important;
    border-color: var(--secondary-blue) !important;
}

.kb-btn-global-outline {
    border: 2px solid var(--primary-blue) !important;
    color: var(--primary-blue) !important;
    background: transparent !important;
}

.kb-btn-global-outline:hover {
    background: var(--primary-blue) !important;
    color: var(--white) !important;
}
</style>
</head>

<body class="page-template-default page page-id-contact">

<div id="wrapper" class="site wp-site-blocks">
	<header id="masthead" class="site-header" role="banner">
		<div class="site-top-header-wrap site-header-row-container site-header-focus-item site-header-row-layout-standard" data-section="kadence_customizer_header_top">
			<div class="site-header-row-container-inner">
				<div class="site-container">
					<div class="site-top-header-inner-wrap site-header-row site-header-row-has-sides site-header-row-no-center">
						<div class="site-header-top-left-wrap site-header-section-left">
							<div class="site-header-item site-header-focus-item" data-section="title_tagline">
								<div class="site-branding branding-layout-standard branding-tablet-layout-inherit branding-mobile-layout-inherit">
									<h1 class="site-title"><a href="index.html" rel="home">Kirkwood Medical Practice</a></h1>
								</div>
							</div>
						</div>
						<div class="site-header-top-right-wrap site-header-section-right">
							<nav class="main-navigation">
								<ul class="nav-menu">
									<li><a href="index.html" class="nav-link">🏠 Home</a></li>
									<li><a href="about.html" class="nav-link">👨‍⚕️ About Us</a></li>
									<li><a href="services.html" class="nav-link">🩺 Services</a></li>
									<li><a href="testimonials.html" class="nav-link">💬 Testimonials</a></li>
									<li><a href="contact.html" class="nav-link active">📍 Contact</a></li>
								</ul>
							</nav>
							<div class="header-button-wrap"><div class="header-button-inner-wrap"><a href="tel:************" target="_self" class="button header-button button-size-medium button-style-filled">Call Us: (*************</a></div></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</header>

	<main id="main" class="site-main" role="main">
		<div class="page-hero">
			<div class="site-container">
				<h1>📍 Let's Stay Connected, Neighbor to Neighbor</h1>
				<p style="font-size: 18px; margin-top: 20px; opacity: 0.9;">
					We're just down the road—and always happy to help. Whether you need to make an appointment, ask a question, or stop in for a visit, we're here when you need us.
				</p>
			</div>
		</div>

		<div class="page-content">
			<div class="site-container">
				<div class="contact-info-grid">
					<div class="contact-info-item">
						<h3>📞 Call Us</h3>
						<p><a href="tel:************">(*************</a></p>
						<p>Monday - Friday: 8:00 AM - 5:00 PM</p>
					</div>
					
					<div class="contact-info-item">
						<h3>📍 Visit Us</h3>
						<p>123 Main Street<br>Kirkwood, NY 13795</p>
						<p><em>Placeholder address - update with actual location</em></p>
					</div>
					
					<div class="contact-info-item">
						<h3>📧 Email Us</h3>
						<p><a href="mailto:<EMAIL>"><EMAIL></a></p>
						<p><em>Placeholder email - update with actual email</em></p>
					</div>
				</div>

				<div style="text-align: center; margin: 60px 0;">
					<h2 style="color: var(--primary-blue); margin-bottom: 20px;">Ready to Get Started?</h2>
					<p style="font-size: 18px; margin-bottom: 30px;">Schedule an appointment or complete your new patient paperwork online.</p>
					
					<div class="wp-block-kadence-advancedbtn kb-buttons-wrap">
						<a class="kb-button kt-button button kt-btn-size-large kt-btn-width-type-auto kb-btn-global-inherit kt-btn-has-text-true wp-block-button__link wp-block-kadence-singlebtn" href="https://consumer.scheduling.athena.io/?locationId=31128-1" target="_blank" style="background-color: var(--primary-blue); color: white; padding: 18px 35px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 15px; font-size: 18px;">
							<span class="kt-btn-inner-text">Schedule an Appointment or Reach Out to Your Local Care Team</span>
						</a>
					</div>
					
					<div style="margin-top: 20px;">
						<a class="kb-button kt-button button kt-btn-size-standard kt-btn-width-type-auto kb-btn-global-outline kt-btn-has-text-true wp-block-kadence-singlebtn" href="http://31128.portal.athenahealth.com/" target="_blank" style="border: 2px solid var(--primary-blue); color: var(--primary-blue); padding: 13px 28px; text-decoration: none; border-radius: 5px; display: inline-block;">
							<span class="kt-btn-inner-text">New Patient Paperwork</span>
						</a>
					</div>
				</div>

				<div class="contact-form">
					<h3 style="color: var(--primary-blue); margin-bottom: 20px; text-align: center;">Send Us a Message</h3>
					<form>
						<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
							<div class="form-group">
								<label for="first-name">First Name *</label>
								<input type="text" id="first-name" name="first-name" required>
							</div>
							<div class="form-group">
								<label for="last-name">Last Name *</label>
								<input type="text" id="last-name" name="last-name" required>
							</div>
						</div>
						
						<div class="form-group">
							<label for="email">Email Address *</label>
							<input type="email" id="email" name="email" required>
						</div>
						
						<div class="form-group">
							<label for="phone">Phone Number</label>
							<input type="tel" id="phone" name="phone">
						</div>
						
						<div class="form-group">
							<label for="message">Message *</label>
							<textarea id="message" name="message" rows="5" required placeholder="How can we help you today?"></textarea>
						</div>
						
						<div style="text-align: center;">
							<button type="submit" style="background-color: var(--primary-blue); color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;">
								Send Message
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</main>

	<footer id="colophon" class="site-footer" role="contentinfo">
		<div class="site-footer-wrap">
			<div class="site-container">
				<div class="site-footer-inner-wrap site-footer-row-container site-footer-row-center-layout-standard">
					<div class="site-footer-row-container-inner">
						<div class="site-footer-bottom-center-wrap site-footer-section-bottom-center">
							<div class="site-footer-item site-footer-focus-item" data-section="kadence_customizer_footer_bottom">
								<div class="footer-widget-area widget-area site-footer-focus-item footer-widget-area-center" data-section="sidebar-widgets-footer-widget-area-center">
									<div class="footer-widget-area-inner site-info inner-link-style-normal">
										<p>&copy; 2025 Kirkwood Medical Practice<br>Proudly serving Kirkwood families with heart, care, and community.</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</footer>
</div>

</body>
</html>
